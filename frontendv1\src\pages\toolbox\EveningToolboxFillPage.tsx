import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  ArrowLeft,
  Moon,
  User,
  Plus,
  Minus,
  Save,
  AlertCircle,
  Briefcase
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_ALL_WORKERS, GET_VALID_JOBS, GET_TODAYS_JOB_RISK_ASSESSMENT } from '../../graphql/queries';
import { FILL_EVENING_TOOLBOX } from '../../graphql/mutations';

interface Worker {
  id: number;
  name: string;
}

interface Job {
  id: number;
  title: string;
  description: string;
}

interface Hazard {
  id: number;
  description: string;
  controlMeasures: { id: number; description: string; }[];
}

interface NewHazard {
  description: string;
  controlMeasures: string[];
}

interface ExistingHazard {
  hazardId: number;
  controlMeasures: string[];
}

interface JobFormData {
  jobId: number;
  newHazards: NewHazard[];
  existingHazards: ExistingHazard[];
  isClosed: boolean;
}

interface FormData {
  conductorId: number;
  jobs: JobFormData[];
}

const EveningToolboxFillPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [formData, setFormData] = useState<FormData>({
    conductorId: 1, // Default as specified in requirements
    jobs: []
  });

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const { data: jobsData, loading: jobsLoading } = useQuery(GET_VALID_JOBS);
  const { data: riskAssessmentData, loading: riskLoading } = useQuery(GET_TODAYS_JOB_RISK_ASSESSMENT);
  const [fillEveningToolbox, { loading: submitting }] = useMutation(FILL_EVENING_TOOLBOX);

  const workers: Worker[] = workersData?.allWorkers || [];
  const jobs: Job[] = jobsData?.validJobs || [];
  const availableHazards: Hazard[] = riskAssessmentData?.todaysJobRiskAssessment?.hazards || [];

  const loading = workersLoading || jobsLoading || riskLoading;

  const addJob = () => {
    const newJob: JobFormData = {
      jobId: 0,
      newHazards: [],
      existingHazards: [],
      isClosed: false
    };
    setFormData(prev => ({
      ...prev,
      jobs: [...prev.jobs, newJob]
    }));
  };

  const removeJob = (index: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.filter((_, i) => i !== index)
    }));
  };

  const updateJob = (index: number, field: keyof JobFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === index ? { ...job, [field]: value } : job
      )
    }));
  };

  const addNewHazard = (jobIndex: number) => {
    const newHazard: NewHazard = {
      description: '',
      controlMeasures: ['']
    };
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, newHazards: [...job.newHazards, newHazard] }
          : job
      )
    }));
  };

  const removeNewHazard = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, newHazards: job.newHazards.filter((_, hi) => hi !== hazardIndex) }
          : job
      )
    }));
  };

  const updateNewHazard = (jobIndex: number, hazardIndex: number, field: keyof NewHazard, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              newHazards: job.newHazards.map((hazard, hi) =>
                hi === hazardIndex ? { ...hazard, [field]: value } : hazard
              )
            }
          : job
      )
    }));
  };

  const addExistingHazard = (jobIndex: number) => {
    const newExistingHazard: ExistingHazard = {
      hazardId: 0,
      controlMeasures: ['']
    };
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, existingHazards: [...job.existingHazards, newExistingHazard] }
          : job
      )
    }));
  };

  const removeExistingHazard = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, existingHazards: job.existingHazards.filter((_, hi) => hi !== hazardIndex) }
          : job
      )
    }));
  };

  const updateExistingHazard = (jobIndex: number, hazardIndex: number, field: keyof ExistingHazard, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              existingHazards: job.existingHazards.map((hazard, hi) =>
                hi === hazardIndex ? { ...hazard, [field]: value } : hazard
              )
            }
          : job
      )
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.conductorId) {
      toast.error('Please select a conductor');
      return false;
    }

    if (formData.jobs.length === 0) {
      toast.error('Please add at least one job');
      return false;
    }

    for (let i = 0; i < formData.jobs.length; i++) {
      const job = formData.jobs[i];
      if (!job.jobId) {
        toast.error(`Please select a job for job ${i + 1}`);
        return false;
      }

      // Validate new hazards
      for (let j = 0; j < job.newHazards.length; j++) {
        const hazard = job.newHazards[j];
        if (!hazard.description.trim()) {
          toast.error(`Please provide description for new hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
        if (hazard.controlMeasures.some(cm => !cm.trim())) {
          toast.error(`Please provide all control measures for new hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
      }

      // Validate existing hazards
      for (let j = 0; j < job.existingHazards.length; j++) {
        const hazard = job.existingHazards[j];
        if (!hazard.hazardId) {
          toast.error(`Please select a hazard for existing hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
        if (hazard.controlMeasures.some(cm => !cm.trim())) {
          toast.error(`Please provide all control measures for existing hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await fillEveningToolbox({
        variables: {
          input: {
            jobs: formData.jobs.map(job => ({
              jobId: job.jobId,
              newHazards: job.newHazards,
              existingHazards: job.existingHazards,
              isClosed: job.isClosed
            })),
            conductorId: formData.conductorId
          }
        }
      });

      if (result.data?.fillEveningToolbox) {
        toast.success('Evening toolbox created successfully!');
        navigate(`/sites/${siteId}/toolbox/evening-toolbox`);
      }
    } catch (error) {
      console.error('Error creating evening toolbox:', error);
      toast.error('Failed to create evening toolbox. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` },
    { name: 'Create', path: `/sites/${siteId}/toolbox/evening-toolbox/fill` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="bg-indigo-50 p-4 rounded-lg">
          <div className="flex items-center space-x-3">
            <Moon className="h-8 w-8 text-indigo-600" />
            <div>
              <h3 className="text-lg font-semibold text-indigo-900">Evening Toolbox Meeting</h3>
              <p className="text-sm text-indigo-700">Configure jobs and their hazards for the evening toolbox</p>
            </div>
          </div>
        </div>

        {/* Conductor Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <User className="h-4 w-4 inline mr-1" />
            Conductor *
          </label>
          <select
            value={formData.conductorId}
            onChange={(e) => setFormData(prev => ({ ...prev, conductorId: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          >
            <option value={0}>Select Conductor</option>
            {workers.map(worker => (
              <option key={worker.id} value={worker.id}>
                {worker.name}
              </option>
            ))}
          </select>
        </div>

        {/* Jobs Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Jobs</h3>
            <button
              type="button"
              onClick={addJob}
              className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              <Plus className="h-4 w-4" />
              <span>Add Job</span>
            </button>
          </div>

          {formData.jobs.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Briefcase className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>No jobs added yet. Click "Add Job" to get started.</p>
            </div>
          )}

          {formData.jobs.map((job, jobIndex) => (
            <div key={jobIndex} className="border border-gray-200 rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-md font-semibold text-gray-800">Job {jobIndex + 1}</h4>
                <button
                  type="button"
                  onClick={() => removeJob(jobIndex)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Minus className="h-4 w-4" />
                </button>

              {/* Job Selection */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Job *</label>
                <select
                  value={job.jobId}
                  onChange={(e) => updateJob(jobIndex, 'jobId', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  required
                >
                  <option value={0}>Select Job</option>
                  {jobs.map(availableJob => (
                    <option key={availableJob.id} value={availableJob.id}>
                      {availableJob.title}
                    </option>
                  ))}
                </select>
              </div>

              {/* Job Closed Status */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`job-closed-${jobIndex}`}
                  checked={job.isClosed}
                  onChange={(e) => updateJob(jobIndex, 'isClosed', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor={`job-closed-${jobIndex}`} className="text-sm font-medium text-gray-700">
                  Job is closed
                </label>
              </div>

              {/* New Hazards */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-semibold text-gray-700">New Hazards</h5>
                  <button
                    type="button"
                    onClick={() => addNewHazard(jobIndex)}
                    className="text-sm text-indigo-600 hover:text-indigo-800"
                  >
                    + Add New Hazard
                  </button>
                </div>

                {job.newHazards.map((hazard, hazardIndex) => (
                  <div key={hazardIndex} className="border border-gray-100 rounded p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">New Hazard {hazardIndex + 1}</span>
                      <button
                        type="button"
                        onClick={() => removeNewHazard(jobIndex, hazardIndex)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Minus className="h-3 w-3" />
                      </button>
                    </div>

                    <input
                      type="text"
                      placeholder="Hazard description"
                      value={hazard.description}
                      onChange={(e) => updateNewHazard(jobIndex, hazardIndex, 'description', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    />

                    <div className="space-y-1">
                      <label className="text-xs font-medium text-gray-600">Control Measures</label>
                      {hazard.controlMeasures.map((measure, measureIndex) => (
                        <div key={measureIndex} className="flex items-center space-x-2">
                          <input
                            type="text"
                            placeholder="Control measure"
                            value={measure}
                            onChange={(e) => {
                              const newMeasures = [...hazard.controlMeasures];
                              newMeasures[measureIndex] = e.target.value;
                              updateNewHazard(jobIndex, hazardIndex, 'controlMeasures', newMeasures);
                            }}
                            className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newMeasures = hazard.controlMeasures.filter((_, i) => i !== measureIndex);
                              updateNewHazard(jobIndex, hazardIndex, 'controlMeasures', newMeasures);
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Minus className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => {
                          const newMeasures = [...hazard.controlMeasures, ''];
                          updateNewHazard(jobIndex, hazardIndex, 'controlMeasures', newMeasures);
                        }}
                        className="text-sm text-indigo-600 hover:text-indigo-800"
                      >
                        + Add Control Measure
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Existing Hazards */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-semibold text-gray-700">Existing Hazards</h5>
                  <button
                    type="button"
                    onClick={() => addExistingHazard(jobIndex)}
                    className="text-sm text-indigo-600 hover:text-indigo-800"
                  >
                    + Add Existing Hazard
                  </button>
                </div>

                {job.existingHazards.map((hazard, hazardIndex) => (
                  <div key={hazardIndex} className="border border-gray-100 rounded p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">Existing Hazard {hazardIndex + 1}</span>
                      <button
                        type="button"
                        onClick={() => removeExistingHazard(jobIndex, hazardIndex)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Minus className="h-3 w-3" />
                      </button>
                    </div>

                    <select
                      value={hazard.hazardId}
                      onChange={(e) => updateExistingHazard(jobIndex, hazardIndex, 'hazardId', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value={0}>Select Existing Hazard</option>
                      {availableHazards.map(availableHazard => (
                        <option key={availableHazard.id} value={availableHazard.id}>
                          {availableHazard.description}
                        </option>
                      ))}
                    </select>

                    <div className="space-y-1">
                      <label className="text-xs font-medium text-gray-600">Control Measures</label>
                      {hazard.controlMeasures.map((measure, measureIndex) => (
                        <div key={measureIndex} className="flex items-center space-x-2">
                          <input
                            type="text"
                            placeholder="Control measure"
                            value={measure}
                            onChange={(e) => {
                              const newMeasures = [...hazard.controlMeasures];
                              newMeasures[measureIndex] = e.target.value;
                              updateExistingHazard(jobIndex, hazardIndex, 'controlMeasures', newMeasures);
                            }}
                            className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newMeasures = hazard.controlMeasures.filter((_, i) => i !== measureIndex);
                              updateExistingHazard(jobIndex, hazardIndex, 'controlMeasures', newMeasures);
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Minus className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => {
                          const newMeasures = [...hazard.controlMeasures, ''];
                          updateExistingHazard(jobIndex, hazardIndex, 'controlMeasures', newMeasures);
                        }}
                        className="text-sm text-indigo-600 hover:text-indigo-800"
                      >
                        + Add Control Measure
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>

          <button
            type="submit"
            disabled={submitting}
            className="flex items-center space-x-2 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Create Evening Toolbox</span>
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default EveningToolboxFillPage;
