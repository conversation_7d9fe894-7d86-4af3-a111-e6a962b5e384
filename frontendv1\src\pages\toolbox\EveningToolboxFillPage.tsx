import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  <PERSON>Left,
  Moon,
  User,
  Briefcase,
  Plus,
  Minus
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { FILL_EVENING_TOOLBOX } from '../../graphql/mutations';
import { GET_ALL_WORKERS, GET_ALL_JOBS, GET_VALID_JOBS } from '../../graphql/queries';

// Types based on GraphQL schema - exact match to graphqlSchema.txt
interface NewHazardInput {
  description: string;
  controlMeasures: string[];
}

interface ExistingHazardInput {
  hazardId: number;
  controlMeasures: string[];
}

interface EveningToolboxJobInput {
  jobId: number;
  newHazards: NewHazardInput[];
  existingHazards: ExistingHazardInput[];
  isClosed: boolean;
}

interface EveningToolboxInput {
  jobs: EveningToolboxJobInput[];
  conductorId: number;
}

interface FormData {
  conductorId: number;
  jobs: EveningToolboxJobInput[];
}

const EveningToolboxFillPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [formData, setFormData] = useState<FormData>({
    conductorId: 1, // Default as specified in requirements
    jobs: []
  });

  const [submitting, setSubmitting] = useState(false);

  // GraphQL queries and mutations
  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const { data: jobsData, loading: jobsLoading } = useQuery(GET_VALID_JOBS);
  const [fillEveningToolbox] = useMutation(FILL_EVENING_TOOLBOX);

  const loading = workersLoading || jobsLoading;

  const breadcrumbs = [
    { name: 'Dashboard', path: `/sites/${siteId}` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` },
    { name: 'Create', path: '#' }
  ];

  // Form handlers
  const addJob = () => {
    const newJob: EveningToolboxJobInput = {
      jobId: 0,
      newHazards: [],
      existingHazards: [],
      isClosed: false
    };
    setFormData(prev => ({
      ...prev,
      jobs: [...prev.jobs, newJob]
    }));
  };

  const removeJob = (index: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.filter((_, i) => i !== index)
    }));
  };

  const updateJob = (index: number, field: keyof EveningToolboxJobInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === index ? { ...job, [field]: value } : job
      )
    }));
  };

  const addNewHazard = (jobIndex: number) => {
    const newHazard: NewHazardInput = {
      description: '',
      controlMeasures: ['']
    };
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, newHazards: [...job.newHazards, newHazard] }
          : job
      )
    }));
  };

  const addExistingHazard = (jobIndex: number) => {
    const existingHazard: ExistingHazardInput = {
      hazardId: 0,
      controlMeasures: ['']
    };
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, existingHazards: [...job.existingHazards, existingHazard] }
          : job
      )
    }));
  };

  const removeExistingHazard = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, existingHazards: job.existingHazards.filter((_, hi) => hi !== hazardIndex) }
          : job
      )
    }));
  };

  const updateExistingHazard = (jobIndex: number, hazardIndex: number, field: keyof ExistingHazardInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              existingHazards: job.existingHazards.map((hazard, hi) =>
                hi === hazardIndex ? { ...hazard, [field]: value } : hazard
              )
            }
          : job
      )
    }));
  };

  const addExistingControlMeasure = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              existingHazards: job.existingHazards.map((hazard, hi) =>
                hi === hazardIndex
                  ? { ...hazard, controlMeasures: [...hazard.controlMeasures, ''] }
                  : hazard
              )
            }
          : job
      )
    }));
  };

  const removeExistingControlMeasure = (jobIndex: number, hazardIndex: number, measureIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              existingHazards: job.existingHazards.map((hazard, hi) =>
                hi === hazardIndex
                  ? { ...hazard, controlMeasures: hazard.controlMeasures.filter((_, mi) => mi !== measureIndex) }
                  : hazard
              )
            }
          : job
      )
    }));
  };

  const updateExistingControlMeasure = (jobIndex: number, hazardIndex: number, measureIndex: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              existingHazards: job.existingHazards.map((hazard, hi) =>
                hi === hazardIndex
                  ? {
                      ...hazard,
                      controlMeasures: hazard.controlMeasures.map((measure, mi) =>
                        mi === measureIndex ? value : measure
                      )
                    }
                  : hazard
              )
            }
          : job
      )
    }));
  };

  const removeNewHazard = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, newHazards: job.newHazards.filter((_, hi) => hi !== hazardIndex) }
          : job
      )
    }));
  };

  const updateNewHazard = (jobIndex: number, hazardIndex: number, field: keyof NewHazardInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              newHazards: job.newHazards.map((hazard, hi) =>
                hi === hazardIndex ? { ...hazard, [field]: value } : hazard
              )
            }
          : job
      )
    }));
  };

  const addControlMeasure = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              newHazards: job.newHazards.map((hazard, hi) =>
                hi === hazardIndex
                  ? { ...hazard, controlMeasures: [...hazard.controlMeasures, ''] }
                  : hazard
              )
            }
          : job
      )
    }));
  };

  const removeControlMeasure = (jobIndex: number, hazardIndex: number, measureIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              newHazards: job.newHazards.map((hazard, hi) =>
                hi === hazardIndex
                  ? { ...hazard, controlMeasures: hazard.controlMeasures.filter((_, mi) => mi !== measureIndex) }
                  : hazard
              )
            }
          : job
      )
    }));
  };

  const updateControlMeasure = (jobIndex: number, hazardIndex: number, measureIndex: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              newHazards: job.newHazards.map((hazard, hi) =>
                hi === hazardIndex
                  ? {
                      ...hazard,
                      controlMeasures: hazard.controlMeasures.map((measure, mi) =>
                        mi === measureIndex ? value : measure
                      )
                    }
                  : hazard
              )
            }
          : job
      )
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.conductorId) {
      toast.error('Please select a conductor');
      return false;
    }

    if (formData.jobs.length === 0) {
      toast.error('Please add at least one job');
      return false;
    }

    for (let i = 0; i < formData.jobs.length; i++) {
      const job = formData.jobs[i];
      if (!job.jobId) {
        toast.error(`Please select a job for job ${i + 1}`);
        return false;
      }

      // Validate new hazards
      for (let j = 0; j < job.newHazards.length; j++) {
        const hazard = job.newHazards[j];
        if (!hazard.description.trim()) {
          toast.error(`Please provide a description for new hazard ${j + 1} in job ${i + 1}`);
          return false;
        }

        const validControlMeasures = hazard.controlMeasures.filter(cm => cm.trim());
        if (validControlMeasures.length === 0) {
          toast.error(`Please provide at least one control measure for new hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
      }

      // Validate existing hazards
      for (let j = 0; j < job.existingHazards.length; j++) {
        const hazard = job.existingHazards[j];
        if (!hazard.hazardId) {
          toast.error(`Please select an existing hazard for existing hazard ${j + 1} in job ${i + 1}`);
          return false;
        }

        const validControlMeasures = hazard.controlMeasures.filter(cm => cm.trim());
        if (validControlMeasures.length === 0) {
          toast.error(`Please provide at least one control measure for existing hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    try {
      // Prepare data according to EveningToolboxInput schema
      const input: EveningToolboxInput = {
        conductorId: formData.conductorId,
        jobs: formData.jobs.map(job => ({
          jobId: job.jobId,
          newHazards: job.newHazards
            .filter(hazard => hazard.description.trim()) // Only include hazards with descriptions
            .map(hazard => ({
              description: hazard.description.trim(),
              controlMeasures: hazard.controlMeasures.filter(cm => cm.trim()) // Only include non-empty control measures
            })),
          existingHazards: job.existingHazards
            .filter(hazard => hazard.hazardId > 0) // Only include hazards with valid IDs
            .map(hazard => ({
              hazardId: hazard.hazardId,
              controlMeasures: hazard.controlMeasures.filter(cm => cm.trim()) // Only include non-empty control measures
            })),
          isClosed: job.isClosed
        }))
      };

      const result = await fillEveningToolbox({
        variables: { input }
      });

      if (result.data?.fillEveningToolbox) {
        toast.success('Evening toolbox created successfully!');
        navigate(`/sites/${siteId}/toolbox/evening-toolbox`);
      }
    } catch (error) {
      console.error('Error creating evening toolbox:', error);
      toast.error('Failed to create evening toolbox. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <span className="ml-2 text-gray-600">Loading...</span>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="bg-indigo-50 p-4 rounded-lg">
          <div className="flex items-center space-x-3">
            <Moon className="h-8 w-8 text-indigo-600" />
            <div>
              <h2 className="text-xl font-semibold text-indigo-900">Evening Toolbox</h2>
              <p className="text-sm text-indigo-700">Record end-of-day safety observations and job status</p>
            </div>
          </div>
        </div>

        {/* Conductor Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <User className="h-4 w-4 inline mr-1" />
            Conductor *
          </label>
          <select
            value={formData.conductorId}
            onChange={(e) => setFormData(prev => ({ ...prev, conductorId: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          >
            <option value="">Select a conductor</option>
            {workersData?.allWorkers?.map((worker: any) => (
              <option key={worker.id} value={worker.id}>
                {worker.name} - {worker.company}
              </option>
            ))}
          </select>
        </div>

        {/* Jobs Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Jobs</h3>
            <button
              type="button"
              onClick={addJob}
              className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              <Plus className="h-4 w-4" />
              <span>Add Job</span>
            </button>
          </div>

          {formData.jobs.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Briefcase className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>No jobs added yet. Click "Add Job" to get started.</p>
            </div>
          )}

          {formData.jobs.map((job, jobIndex) => (
            <div key={jobIndex} className="border border-gray-200 rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-md font-semibold text-gray-800">Job {jobIndex + 1}</h4>
                <button
                  type="button"
                  onClick={() => removeJob(jobIndex)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Minus className="h-4 w-4" />
                </button>
              </div>

              {/* Job Selection */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Job *</label>
                <select
                  value={job.jobId}
                  onChange={(e) => updateJob(jobIndex, 'jobId', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  required
                >
                  <option value="">Select a job</option>
                  {jobsData?.allJobs?.map((jobOption: any) => (
                    <option key={jobOption.id} value={jobOption.id}>
                      {jobOption.title} - {jobOption.location}
                    </option>
                  ))}
                </select>
              </div>

              {/* Job Closed Status */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`job-closed-${jobIndex}`}
                  checked={job.isClosed}
                  onChange={(e) => updateJob(jobIndex, 'isClosed', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor={`job-closed-${jobIndex}`} className="text-sm text-gray-700">
                  Job is closed/completed
                </label>
              </div>

              {/* New Hazards */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-semibold text-gray-700">New Hazards</h5>
                  <button
                    type="button"
                    onClick={() => addNewHazard(jobIndex)}
                    className="text-sm text-indigo-600 hover:text-indigo-800"
                  >
                    + Add New Hazard
                  </button>
                </div>

                {job.newHazards.map((hazard, hazardIndex) => (
                  <div key={hazardIndex} className="border border-gray-100 rounded p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-600">New Hazard {hazardIndex + 1}</span>
                      <button
                        type="button"
                        onClick={() => removeNewHazard(jobIndex, hazardIndex)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Minus className="h-3 w-3" />
                      </button>
                    </div>

                    <div className="space-y-1">
                      <label className="text-xs font-medium text-gray-600">Description</label>
                      <textarea
                        placeholder="Describe the hazard"
                        value={hazard.description}
                        onChange={(e) => updateNewHazard(jobIndex, hazardIndex, 'description', e.target.value)}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        rows={2}
                      />
                    </div>

                    <div className="space-y-1">
                      <label className="text-xs font-medium text-gray-600">Control Measures</label>
                      {hazard.controlMeasures.map((measure, measureIndex) => (
                        <div key={measureIndex} className="flex items-center space-x-2">
                          <input
                            type="text"
                            placeholder="Control measure"
                            value={measure}
                            onChange={(e) => updateControlMeasure(jobIndex, hazardIndex, measureIndex, e.target.value)}
                            className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                          <button
                            type="button"
                            onClick={() => removeControlMeasure(jobIndex, hazardIndex, measureIndex)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Minus className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => addControlMeasure(jobIndex, hazardIndex)}
                        className="text-sm text-indigo-600 hover:text-indigo-800"
                      >
                        + Add Control Measure
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Existing Hazards */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-semibold text-gray-700">Existing Hazards</h5>
                  <button
                    type="button"
                    onClick={() => addExistingHazard(jobIndex)}
                    className="text-sm text-indigo-600 hover:text-indigo-800"
                  >
                    + Add Existing Hazard
                  </button>
                </div>

                {job.existingHazards.map((hazard, hazardIndex) => {
                  const selectedJob = jobsData?.allJobs?.find((j: any) => j.id === job.jobId);
                  const availableHazards = selectedJob?.hazards || [];

                  return (
                    <div key={hazardIndex} className="border border-gray-100 rounded p-3 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600">Existing Hazard {hazardIndex + 1}</span>
                        <button
                          type="button"
                          onClick={() => removeExistingHazard(jobIndex, hazardIndex)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Minus className="h-3 w-3" />
                        </button>
                      </div>

                      <div className="space-y-1">
                        <label className="text-xs font-medium text-gray-600">Select Hazard</label>
                        <select
                          value={hazard.hazardId}
                          onChange={(e) => updateExistingHazard(jobIndex, hazardIndex, 'hazardId', parseInt(e.target.value))}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        >
                          <option value="">Select an existing hazard</option>
                          {availableHazards.map((h: any) => (
                            <option key={h.id} value={h.id}>
                              {h.description}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="space-y-1">
                        <label className="text-xs font-medium text-gray-600">Additional Control Measures</label>
                        {hazard.controlMeasures.map((measure, measureIndex) => (
                          <div key={measureIndex} className="flex items-center space-x-2">
                            <input
                              type="text"
                              placeholder="Additional control measure"
                              value={measure}
                              onChange={(e) => updateExistingControlMeasure(jobIndex, hazardIndex, measureIndex, e.target.value)}
                              className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingControlMeasure(jobIndex, hazardIndex, measureIndex)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Minus className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => addExistingControlMeasure(jobIndex, hazardIndex)}
                          className="text-sm text-indigo-600 hover:text-indigo-800"
                        >
                          + Add Control Measure
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>

          <button
            type="submit"
            disabled={submitting}
            className="flex items-center space-x-2 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Moon className="h-4 w-4" />
                <span>Create Evening Toolbox</span>
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default EveningToolboxFillPage;